import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	updateTask,
	addTaskComment,
	updateTaskDescription,
} from '@/lib/features/tasks/tasksSlice';

/**
 * Custom hook for managing task-related handlers
 * Handles form submissions and API calls
 */
export const useTaskHandlers = (taskId, commentForm, onSave, onClose) => {
	const dispatch = useAppDispatch();
	const { currentProject } = useAppSelector((store) => store.projects);

	// Form submission handlers
	const handleUpdateTask = async (data) => {
		try {
			await dispatch(
				updateTask({
					projectId: currentProject?._id,
					taskData: data,
				})
			);
			onSave?.();
			onClose();
		} catch (error) {
			console.error('Error updating task:', error);
		}
	};

	const handleAddComment = async (data) => {
		try {
			await dispatch(
				addTaskComment({
					projectId: currentProject?._id,
					commentData: data,
				})
			);
			commentForm.reset({
				taskId: taskId,
				comment: '',
			});
		} catch (error) {
			console.error('Error adding comment:', error);
		}
	};

	const handleUpdateDescription = async (data) => {
		try {
			await dispatch(
				updateTaskDescription({
					projectId: currentProject?._id,
					descriptionData: data,
				})
			);
		} catch (error) {
			console.error('Error updating description:', error);
		}
	};

	return {
		handleUpdateTask,
		handleAddComment,
		handleUpdateDescription,
	};
};
