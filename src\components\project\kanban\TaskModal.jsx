'use client';

import React, { useState, useEffect } from 'react';
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	X,
	User,
	Paperclip,
	MessageSquare,
	Image as ImageIcon,
	Trash2,
	Edit3,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { RichTextEditor } from './RichTextEditor';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchSingleTaskDetails } from '@/lib/features/tasks/tasksSlice';

// Sample assignees data (in real app, this would come from API)
const sampleAssignees = [
	{ id: 1, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 2, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 3, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 4, name: 'Sarah Wilson', email: '<EMAIL>', avatar: null },
];

// Sample attachments with images
const sampleAttachments = [
	{
		id: 1,
		name: 'design-mockup.jpg',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=1',
		size: '2.3 MB',
	},
	{
		id: 2,
		name: 'project-brief.pdf',
		type: 'document',
		url: '#',
		size: '1.1 MB',
	},
	{
		id: 3,
		name: 'screenshot.png',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=2',
		size: '856 KB',
	},
];

/**
 * TaskModal Component
 * Modal for adding/editing tasks with rich text editor
 */
export const TaskModal = ({
	isOpen,
	onClose,
	// task = null,
	taskId = null,
	onSave,
	isGlassMode = false,
}) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task } = useAppSelector((store) => store.tasks);
	console.log(task, 'opne');
	const [taskData, setTaskData] = useState({
		title: '',
		description: '',
		assignee: null,
		coverImageId: null, // ID of attachment used as cover
		attachments: [],
		comments: [],
	});
	const [newComment, setNewComment] = useState('');
	const [isEditingTitle, setIsEditingTitle] = useState(false);
	const [isEditingDescription, setIsEditingDescription] = useState(false);
	const [tempDescription, setTempDescription] = useState('');

	const isEditMode = !!taskId;

	// Helper functions for cover image management
	const getImageAttachments = () => {
		return taskData.attachments.filter((att) => att.type === 'image');
	};

	const getCoverImage = () => {
		if (!taskData.coverImageId) return null;
		return taskData.attachments.find((att) => att.id === taskData.coverImageId);
	};

	const getDefaultCoverImage = () => {
		const imageAttachments = getImageAttachments();
		return imageAttachments.length > 0 ? imageAttachments[0] : null;
	};

	useEffect(() => {
		console.log(taskId, 'running');
		if (taskId) {
			dispatch(fetchSingleTaskDetails(taskId));
		}
	}, [dispatch, taskId]);

	// Initialize task data when modal opens
	// useEffect(() => {
	// 	console.log('TaskModal useEffect:', { isOpen, isEditMode, task });
	// 	if (isOpen && isEditMode && task) {
	// 		// Map API task data structure to modal data structure
	// 		const attachments = task.media || sampleAttachments; // Use API media field or fallback to sample
	// 		const defaultCover =
	// 			task.coverImage ||
	// 			attachments.find((att) => att.type === 'image' || att.url)?.url;

	// 		setTaskData({
	// 			title: task.name || task.title || '', // API uses 'name' field
	// 			description: task.description || '',
	// 			assignee: task.assignedTo || null, // API uses 'assignedTo' field
	// 			coverImageId: defaultCover || null,
	// 			attachments: attachments,
	// 			comments: task.comments || [],
	// 		});
	// 		setNewComment('');
	// 		setIsEditingTitle(false);
	// 		setIsEditingDescription(false);
	// 		setTempDescription(task.description || '');
	// 	} else if (isOpen && !isEditMode) {
	// 		// Reset for new task creation
	// 		setTaskData({
	// 			title: '',
	// 			description: '',
	// 			assignee: null,
	// 			coverImageId: null,
	// 			attachments: [],
	// 			comments: [],
	// 		});
	// 		setNewComment('');
	// 		setIsEditingTitle(false);
	// 		setIsEditingDescription(false);
	// 		setTempDescription('');
	// 	}
	// }, [isOpen, isEditMode, task]);

	const handleSave = () => {
		if (!taskData.title.trim()) return;

		const savedTask = {
			...task,
			...taskData,
			id: task?.id || `task-${Date.now()}`,
			updatedAt: new Date().toISOString(),
		};

		onSave?.(savedTask);
		onClose();
	};

	const handleAddComment = () => {
		if (!newComment.trim()) return;

		const comment = {
			id: `comment-${Date.now()}`,
			content: newComment,
			author: 'Current User', // In real app, get from auth
			createdAt: new Date().toISOString(),
		};

		setTaskData((prev) => ({
			...prev,
			comments: [...prev.comments, comment],
		}));
		setNewComment('');
	};

	const handleRemoveCoverImage = () => {
		setTaskData((prev) => ({
			...prev,
			coverImageId: null,
		}));
	};

	const handleEditDescription = () => {
		setTempDescription(taskData.description);
		setIsEditingDescription(true);
	};

	const handleSaveDescription = () => {
		setTaskData((prev) => ({
			...prev,
			description: tempDescription,
		}));
		setIsEditingDescription(false);
	};

	const handleCancelDescription = () => {
		setTempDescription(taskData.description);
		setIsEditingDescription(false);
	};

	const handleSetCoverImage = (attachmentId) => {
		setTaskData((prev) => ({
			...prev,
			coverImageId: attachmentId,
		}));
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
				{/* Top Section - Full Width */}
				<div className="relative">
					{/* Cover Image */}
					{getCoverImage() && (
						<div className="relative w-full h-32 bg-gray-100 flex items-center justify-center">
							<img
								src={getCoverImage().url}
								alt="Task cover"
								className="w-48 h-24 object-cover rounded-lg shadow-sm"
							/>
							<Button
								variant="ghost"
								size="sm"
								onClick={handleRemoveCoverImage}
								className="absolute bottom-2 left-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70"
							>
								<Trash2 className="h-3 w-3 mr-1" />
								Remove Cover
							</Button>
						</div>
					)}

					{/* Header with Close and Assignee */}
					<div className="flex items-center justify-between p-4 border-b">
						<div className="flex items-center gap-3">
							<User className="h-5 w-5 text-gray-500" />
							<Select
								value={taskData.assignee?.id?.toString() || ''}
								onValueChange={(value) => {
									const assignee = sampleAssignees.find(
										(a) => a.id.toString() === value
									);
									setTaskData((prev) => ({ ...prev, assignee }));
								}}
							>
								<SelectTrigger className="w-48">
									<SelectValue placeholder="Select assignee" />
								</SelectTrigger>
								<SelectContent>
									{sampleAssignees.map((assignee) => (
										<SelectItem
											key={assignee.id}
											value={assignee.id.toString()}
										>
											<div className="flex items-center gap-2">
												<Avatar className="h-6 w-6">
													<AvatarImage src={assignee.avatar} />
													<AvatarFallback className="text-xs">
														{assignee.name
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</AvatarFallback>
												</Avatar>
												<span>{assignee.name}</span>
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<Button
							variant="ghost"
							size="sm"
							onClick={onClose}
							className="h-8 w-8 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</div>

				{/* Main Content - Two Columns */}
				<div className="flex h-[calc(90vh-200px)]">
					{/* Left Section - Larger */}
					<div className="flex-1 p-6 pr-3 overflow-y-auto">
						{/* Task Title */}
						<div className="mb-6">
							{isEditingTitle ? (
								<Input
									value={taskData.title}
									onChange={(e) =>
										setTaskData((prev) => ({ ...prev, title: e.target.value }))
									}
									onBlur={() => setIsEditingTitle(false)}
									onKeyDown={(e) => {
										if (e.key === 'Enter') setIsEditingTitle(false);
									}}
									placeholder="Task title..."
									className="text-xl font-semibold border-none p-0 h-auto focus-visible:ring-0"
									autoFocus
								/>
							) : (
								<h2
									onClick={() => setIsEditingTitle(true)}
									className="text-xl font-semibold cursor-pointer hover:bg-gray-50 p-2 rounded -m-2"
								>
									{taskData.title || 'Click to add title...'}
								</h2>
							)}
						</div>

						{/* Description */}
						<div className="mb-6">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
									<span>Description</span>
								</h3>
								{!isEditingDescription && (
									<Button
										variant="outline"
										size="sm"
										onClick={handleEditDescription}
										className="h-7 px-2 text-xs"
									>
										Edit
									</Button>
								)}
							</div>

							{isEditingDescription ? (
								<div className="space-y-2">
									<RichTextEditor
										content={tempDescription}
										onChange={setTempDescription}
										placeholder="Add a more detailed description..."
									/>
									<div className="flex items-center gap-2 justify-end">
										<Button
											variant="outline"
											size="sm"
											onClick={handleCancelDescription}
											className="h-7 px-2 text-xs"
										>
											Cancel
										</Button>
										<Button
											size="sm"
											onClick={handleSaveDescription}
											className="h-7 px-2 text-xs"
										>
											Save
										</Button>
									</div>
								</div>
							) : (
								<div className="min-h-[120px] p-3 border rounded-md bg-gray-50">
									{taskData.description ? (
										<div
											className="text-sm text-gray-700 whitespace-pre-wrap"
											dangerouslySetInnerHTML={{ __html: taskData.description }}
										/>
									) : (
										<p className="text-sm text-gray-500 italic">
											No description added yet. Click Edit to add one.
										</p>
									)}
								</div>
							)}
						</div>

						{/* Attachments */}
						<div>
							<div className="flex items-center justify-between mb-3">
								<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
									<Paperclip className="h-4 w-4" />
									Attachments
								</h3>
								<Button variant="outline" size="sm">
									Add
								</Button>
							</div>

							{taskData.attachments.length > 0 ? (
								<div className="space-y-2">
									{taskData.attachments.map((attachment) => (
										<div
											key={attachment.id}
											className={cn(
												'flex items-center gap-3 p-2 border rounded-lg hover:bg-gray-50 transition-colors',
												attachment.id === taskData.coverImageId &&
													'bg-blue-50 border-blue-200'
											)}
										>
											<div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
												{attachment.type === 'image' ? (
													<img
														src={attachment.url}
														alt={attachment.name}
														className="w-8 h-8 object-cover rounded"
													/>
												) : (
													<Paperclip className="h-4 w-4 text-gray-500" />
												)}
											</div>
											<div className="flex-1">
												<p className="text-sm font-medium">{attachment.name}</p>
												<p className="text-xs text-gray-500">
													{attachment.size}
												</p>
												{attachment.id === taskData.coverImageId && (
													<p className="text-xs text-blue-600 font-medium">
														Cover Image
													</p>
												)}
											</div>
											{attachment.type === 'image' && (
												<div className="flex gap-1">
													{attachment.id === taskData.coverImageId ? (
														<Button
															variant="ghost"
															size="sm"
															onClick={handleRemoveCoverImage}
															className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
														>
															Remove Cover
														</Button>
													) : (
														<Button
															variant="ghost"
															size="sm"
															onClick={() => handleSetCoverImage(attachment.id)}
															className="h-7 px-2 text-xs text-blue-600 hover:text-blue-700"
														>
															Make Cover
														</Button>
													)}
												</div>
											)}
										</div>
									))}
								</div>
							) : (
								<p className="text-sm text-gray-500">No attachments yet</p>
							)}
						</div>
					</div>

					{/* Right Section - Comments */}
					<div className="w-80 border-l bg-gray-50/50 p-6 pl-3 flex flex-col">
						<div className="flex items-center gap-2 mb-4">
							<MessageSquare className="h-4 w-4 text-gray-500" />
							<h3 className="text-sm font-medium text-gray-700">
								Comments and activity
							</h3>
						</div>

						{/* Comments List */}
						<div className="flex-1 overflow-y-auto mb-4 space-y-3">
							{taskData.comments.map((comment) => (
								<div key={comment.id} className="flex gap-2">
									<Avatar className="h-6 w-6 mt-1">
										<AvatarFallback className="text-xs">
											{comment.author
												.split(' ')
												.map((n) => n[0])
												.join('')}
										</AvatarFallback>
									</Avatar>
									<div className="flex-1">
										<div className="bg-white p-3 rounded-lg shadow-sm">
											<div className="flex items-center gap-2 mb-1">
												<span className="text-sm font-medium">
													{comment.author}
												</span>
												<span className="text-xs text-gray-500">
													{new Date(comment.createdAt).toLocaleDateString()}
												</span>
											</div>
											<div
												className="text-sm text-gray-700"
												dangerouslySetInnerHTML={{ __html: comment.content }}
											/>
										</div>
									</div>
								</div>
							))}
						</div>

						{/* Add Comment */}
						<div className="border-t pt-4">
							<RichTextEditor
								content={newComment}
								onChange={setNewComment}
								placeholder="Write a comment..."
								compact
							/>
							<div className="flex justify-end mt-2">
								<Button
									size="sm"
									onClick={handleAddComment}
									disabled={!newComment.trim()}
								>
									Comment
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Footer */}
				<div className="flex items-center justify-end p-4 border-t bg-gray-50/50">
					<div className="flex items-center gap-2">
						<Button variant="outline" onClick={onClose}>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={!taskData.title.trim()}>
							Save Changes
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};
