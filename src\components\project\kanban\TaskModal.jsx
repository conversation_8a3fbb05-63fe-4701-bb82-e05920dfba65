'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { X, User, Paperclip, MessageSquare, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RichTextEditor } from './RichTextEditor';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchSingleTaskDetails,
	updateTask,
	addTaskComment,
	updateTaskDescription,
} from '@/lib/features/tasks/tasksSlice';
import {
	updateTaskSchema,
	addCommentSchema,
	updateTaskDescriptionSchema,
} from '../../../../schema';

// Sample assignees data (in real app, this would come from API)
const sampleAssignees = [
	{ id: 1, name: 'John Doe', email: '<EMAIL>', avatar: null },
	{ id: 2, name: 'Jane Smith', email: '<EMAIL>', avatar: null },
	{ id: 3, name: 'Mike Johnson', email: '<EMAIL>', avatar: null },
	{ id: 4, name: 'Sarah Wilson', email: '<EMAIL>', avatar: null },
];

// Sample attachments with images
const sampleAttachments = [
	{
		id: 1,
		name: 'design-mockup.jpg',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=1',
		size: '2.3 MB',
	},
	{
		id: 2,
		name: 'project-brief.pdf',
		type: 'document',
		url: '#',
		size: '1.1 MB',
	},
	{
		id: 3,
		name: 'screenshot.png',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=2',
		size: '856 KB',
	},
];

/**
 * TaskModal Component
 * Modal for adding/editing tasks with react-hook-form and Zod validation
 */
export const TaskModal = ({
	isOpen,
	onClose,
	taskId = null,
	onSave,
	isGlassMode = false,
}) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task, isLoading } = useAppSelector(
		(store) => store.tasks
	);
	const { currentProject } = useAppSelector((store) => store.projects);

	const isEditMode = !!taskId;

	// Form for updating task details
	const taskForm = useForm({
		resolver: zodResolver(updateTaskSchema),
		defaultValues: {
			taskId: taskId || '',
			name: '',
			assignedTo: '',
			dueDate: '',
			priority: 'medium',
			coverImage: '',
			color: '',
		},
	});

	// Form for adding comments
	const commentForm = useForm({
		resolver: zodResolver(addCommentSchema),
		defaultValues: {
			taskId: taskId || '',
			comment: '',
		},
	});

	// Form for updating description
	const descriptionForm = useForm({
		resolver: zodResolver(updateTaskDescriptionSchema),
		defaultValues: {
			taskId: taskId || '',
			description: '',
		},
	});

	// Fetch task details when taskId changes
	useEffect(() => {
		if (taskId) {
			dispatch(fetchSingleTaskDetails(taskId));
		}
	}, [dispatch, taskId]);

	// Update form values when task data is loaded
	useEffect(() => {
		if (task && isEditMode) {
			taskForm.reset({
				taskId: task._id || taskId,
				name: task.name || '',
				description: task.description || '',
				assignedTo: task.assignedTo || '',
				dueDate: task.dueDate || '',
				priority: task.priority || 'medium',
				coverImage: task.coverImage || '',
				color: task.color || '',
			});

			descriptionForm.reset({
				taskId: task._id || taskId,
				description: task.description || '',
			});

			commentForm.reset({
				taskId: task._id || taskId,
				comment: '',
			});
		}
	}, [task, isEditMode, taskId, taskForm, descriptionForm, commentForm]);

	// Form submission handlers
	const handleUpdateTask = async (data) => {
		try {
			await dispatch(
				updateTask({
					projectId: currentProject?._id,
					taskData: data,
				})
			);
			onSave?.();
			onClose();
		} catch (error) {
			console.error('Error updating task:', error);
		}
	};

	const handleAddComment = async (data) => {
		try {
			await dispatch(
				addTaskComment({
					projectId: currentProject?._id,
					commentData: data,
				})
			);
			commentForm.reset({
				taskId: taskId,
				comment: '',
			});
		} catch (error) {
			console.error('Error adding comment:', error);
		}
	};

	const handleUpdateDescription = async (data) => {
		try {
			await dispatch(
				updateTaskDescription({
					projectId: currentProject?._id,
					descriptionData: data,
				})
			);
		} catch (error) {
			console.error('Error updating description:', error);
		}
	};

	// Helper functions for cover image management
	const getImageAttachments = () => {
		return (
			task?.media?.filter((att) => att.type === 'image') ||
			sampleAttachments.filter((att) => att.type === 'image')
		);
	};

	const getCoverImage = () => {
		const coverImageUrl = taskForm.watch('coverImage');
		if (!coverImageUrl) return null;
		return getImageAttachments().find((att) => att.url === coverImageUrl);
	};

	const handleRemoveCoverImage = () => {
		taskForm.setValue('coverImage', '');
	};

	const handleSetCoverImage = (attachmentUrl) => {
		taskForm.setValue('coverImage', attachmentUrl);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
				<Form {...taskForm}>
					<form onSubmit={taskForm.handleSubmit(handleUpdateTask)}>
						{/* Top Section - Full Width */}
						<div className="relative">
							{/* Cover Image */}
							{getCoverImage() && (
								<div className="relative w-full h-32 bg-gray-100 flex items-center justify-center">
									<img
										src={getCoverImage().url}
										alt="Task cover"
										className="w-48 h-24 object-cover rounded-lg shadow-sm"
									/>
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={handleRemoveCoverImage}
										className="absolute bottom-2 left-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70"
									>
										<Trash2 className="h-3 w-3 mr-1" />
										Remove Cover
									</Button>
								</div>
							)}

							{/* Header with Close and Assignee */}
							<div className="flex items-center justify-between p-4 border-b">
								<div className="flex items-center gap-3">
									<User className="h-5 w-5 text-gray-500" />
									<FormField
										control={taskForm.control}
										name="assignedTo"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger className="w-48">
															<SelectValue placeholder="Select assignee" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{sampleAssignees.map((assignee) => (
															<SelectItem
																key={assignee.id}
																value={assignee.id.toString()}
															>
																<div className="flex items-center gap-2">
																	<Avatar className="h-6 w-6">
																		<AvatarImage src={assignee.avatar} />
																		<AvatarFallback className="text-xs">
																			{assignee.name
																				.split(' ')
																				.map((n) => n[0])
																				.join('')}
																		</AvatarFallback>
																	</Avatar>
																	<span>{assignee.name}</span>
																</div>
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<Button
									type="button"
									variant="ghost"
									size="sm"
									onClick={onClose}
									className="h-8 w-8 p-0"
								>
									<X className="h-4 w-4" />
								</Button>
							</div>
						</div>

						{/* Main Content - Two Columns */}
						<div className="flex h-[calc(90vh-200px)]">
							{/* Left Section - Larger */}
							<div className="flex-1 p-6 pr-3 overflow-y-auto">
								{/* Task Title */}
								<div className="mb-6">
									<FormField
										control={taskForm.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														{...field}
														placeholder="Task title..."
														className="text-xl font-semibold border-none p-0 h-auto focus-visible:ring-0"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								{/* Description */}
								<div className="mb-6">
									<div className="flex items-center justify-between mb-2">
										<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
											<span>Description</span>
										</h3>
									</div>

									<Form {...descriptionForm}>
										<form
											onSubmit={descriptionForm.handleSubmit(
												handleUpdateDescription
											)}
										>
											<FormField
												control={descriptionForm.control}
												name="description"
												render={({ field }) => (
													<FormItem>
														<FormControl>
															<RichTextEditor
																content={field.value}
																onChange={field.onChange}
																placeholder="Add a more detailed description..."
															/>
														</FormControl>
														<FormMessage />
														<div className="flex items-center gap-2 justify-end mt-2">
															<Button
																type="submit"
																size="sm"
																className="h-7 px-2 text-xs"
															>
																Save Description
															</Button>
														</div>
													</FormItem>
												)}
											/>
										</form>
									</Form>
								</div>

								{/* Attachments */}
								<div>
									<div className="flex items-center justify-between mb-3">
										<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
											<Paperclip className="h-4 w-4" />
											Attachments
										</h3>
										<Button type="button" variant="outline" size="sm">
											Add
										</Button>
									</div>

									{getImageAttachments().length > 0 ? (
										<div className="space-y-2">
											{getImageAttachments().map((attachment) => (
												<div
													key={attachment.id || attachment.url}
													className={cn(
														'flex items-center gap-3 p-2 border rounded-lg hover:bg-gray-50 transition-colors',
														attachment.url === taskForm.watch('coverImage') &&
															'bg-blue-50 border-blue-200'
													)}
												>
													<div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
														{attachment.type === 'image' ? (
															<img
																src={attachment.url}
																alt={attachment.name}
																className="w-8 h-8 object-cover rounded"
															/>
														) : (
															<Paperclip className="h-4 w-4 text-gray-500" />
														)}
													</div>
													<div className="flex-1">
														<p className="text-sm font-medium">
															{attachment.name}
														</p>
														<p className="text-xs text-gray-500">
															{attachment.size}
														</p>
														{attachment.url ===
															taskForm.watch('coverImage') && (
															<p className="text-xs text-blue-600 font-medium">
																Cover Image
															</p>
														)}
													</div>
													{attachment.type === 'image' && (
														<div className="flex gap-1">
															{attachment.url ===
															taskForm.watch('coverImage') ? (
																<Button
																	type="button"
																	variant="ghost"
																	size="sm"
																	onClick={handleRemoveCoverImage}
																	className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
																>
																	Remove Cover
																</Button>
															) : (
																<Button
																	type="button"
																	variant="ghost"
																	size="sm"
																	onClick={() =>
																		handleSetCoverImage(attachment.url)
																	}
																	className="h-7 px-2 text-xs text-blue-600 hover:text-blue-700"
																>
																	Make Cover
																</Button>
															)}
														</div>
													)}
												</div>
											))}
										</div>
									) : (
										<p className="text-sm text-gray-500">No attachments yet</p>
									)}
								</div>
							</div>

							{/* Right Section - Comments */}
							<div className="w-80 border-l bg-gray-50/50 p-6 pl-3 flex flex-col">
								<div className="flex items-center gap-2 mb-4">
									<MessageSquare className="h-4 w-4 text-gray-500" />
									<h3 className="text-sm font-medium text-gray-700">
										Comments and activity
									</h3>
								</div>

								{/* Comments List */}
								<div className="flex-1 overflow-y-auto mb-4 space-y-3">
									{task?.comments?.map((comment) => (
										<div key={comment._id || comment.id} className="flex gap-2">
											<Avatar className="h-6 w-6 mt-1">
												<AvatarFallback className="text-xs">
													{comment.author
														?.split(' ')
														.map((n) => n[0])
														.join('') || 'U'}
												</AvatarFallback>
											</Avatar>
											<div className="flex-1">
												<div className="bg-white p-3 rounded-lg shadow-sm">
													<div className="flex items-center gap-2 mb-1">
														<span className="text-sm font-medium">
															{comment.author || 'User'}
														</span>
														<span className="text-xs text-gray-500">
															{new Date(comment.createdAt).toLocaleDateString()}
														</span>
													</div>
													<div
														className="text-sm text-gray-700"
														dangerouslySetInnerHTML={{
															__html: comment.content,
														}}
													/>
												</div>
											</div>
										</div>
									))}
								</div>

								{/* Add Comment */}
								<div className="border-t pt-4">
									<Form {...commentForm}>
										<form onSubmit={commentForm.handleSubmit(handleAddComment)}>
											<FormField
												control={commentForm.control}
												name="comment"
												render={({ field }) => (
													<FormItem>
														<FormControl>
															<RichTextEditor
																content={field.value}
																onChange={field.onChange}
																placeholder="Write a comment..."
																compact
															/>
														</FormControl>
														<FormMessage />
														<div className="flex justify-end mt-2">
															<Button
																type="submit"
																size="sm"
																disabled={!field.value.trim()}
															>
																Comment
															</Button>
														</div>
													</FormItem>
												)}
											/>
										</form>
									</Form>
								</div>
							</div>
						</div>

						{/* Footer */}
						<div className="flex items-center justify-end p-4 border-t bg-gray-50/50">
							<div className="flex items-center gap-2">
								<Button type="button" variant="outline" onClick={onClose}>
									Cancel
								</Button>
								<Button type="submit">Save Changes</Button>
							</div>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
};
