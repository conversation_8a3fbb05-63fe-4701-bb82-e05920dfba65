import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchSingleTaskDetails } from '@/lib/features/tasks/tasksSlice';

// Sample attachments with images (in real app, this would come from API)
const sampleAttachments = [
	{
		id: 1,
		name: 'design-mockup.jpg',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=1',
		size: '2.3 MB',
	},
	{
		id: 2,
		name: 'project-brief.pdf',
		type: 'document',
		url: '#',
		size: '1.1 MB',
	},
	{
		id: 3,
		name: 'screenshot.png',
		type: 'image',
		url: 'https://picsum.photos/400/200?random=2',
		size: '856 KB',
	},
];

/**
 * Custom hook for managing task data and attachments
 * Handles data fetching and attachment management
 */
export const useTaskData = (taskId, taskForm, updateFormsWithTaskData) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task, isLoading } = useAppSelector(
		(store) => store.tasks
	);

	// Fetch task details when taskId changes
	useEffect(() => {
		if (taskId) {
			dispatch(fetchSingleTaskDetails(taskId));
		}
	}, [dispatch, taskId]);

	// Update forms when task data is loaded
	useEffect(() => {
		if (task && updateFormsWithTaskData) {
			updateFormsWithTaskData(task);
		}
	}, [task, updateFormsWithTaskData]);

	// Helper functions for cover image management
	const getImageAttachments = () => {
		return (
			task?.media?.filter((att) => att.type === 'image') ||
			sampleAttachments.filter((att) => att.type === 'image')
		);
	};

	const getCoverImage = () => {
		const coverImageUrl = taskForm.watch('coverImage');
		if (!coverImageUrl) return null;
		return getImageAttachments().find((att) => att.url === coverImageUrl);
	};

	const handleRemoveCoverImage = () => {
		taskForm.setValue('coverImage', '');
	};

	const handleSetCoverImage = (attachmentUrl) => {
		taskForm.setValue('coverImage', attachmentUrl);
	};

	return {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	};
};
