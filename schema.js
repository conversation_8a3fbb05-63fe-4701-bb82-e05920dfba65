const createTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	companyId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Company is required'),
});

const updateTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
	bgColor: z
		.string()
		.regex(
			/^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
			'Color must be a valid hex color code'
		)
		.optional(),
});

const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	groupId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Group is required'),
});

const updateTaskSchema = z.object({
	taskId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Task ID is required'),
	name: z.string().optional(),
	description: z.string().optional(),
	assignedTo: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.optional(),
	dueDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Due date must be in YYYY-MM-DD format')
		.optional(),
	priority: z.enum(['low', 'medium', 'high']).optional(),
	coverImage: z.string().optional(),
	color: z
		.string()
		.regex(
			/^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
			'Color must be a valid hex color code'
		)
		.optional(),
});
